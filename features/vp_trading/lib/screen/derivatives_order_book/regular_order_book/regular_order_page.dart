import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/cubit/derivatives_order_book/derivatives_order_book_multi_select_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/derivatives_order_list.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/derivatives_order_title_widget.dart';
import 'package:vp_trading/screen/derivatives_order_book/widget/derivatives_order_cancel_confirmation_dialog.dart';
import 'package:vp_trading/screen/derivatives_order_book/widget/derivatives_order_multi_select_bottom_bar.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class RegularOrderPage extends StatefulWidget {
  const RegularOrderPage({super.key});

  @override
  State<RegularOrderPage> createState() => _RegularOrderPageState();
}

class _RegularOrderPageState extends State<RegularOrderPage> {
  late DerivativesOrderBookCubit _cubit;
  late DerivativesOrderBookMultiSelectCubit _multiSelectCubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<DerivativesOrderBookCubit>();
    _multiSelectCubit = DerivativesOrderBookMultiSelectCubit();
  }

  @override
  void dispose() {
    _multiSelectCubit.close();
    super.dispose();
  }

  void _handleEnterMultiSelectMode() {
    _multiSelectCubit.enterMultiSelectMode();
  }

  void _handleExitMultiSelectMode() {
    _multiSelectCubit.exitMultiSelectMode();
  }

  Future<void> _handleCancelSelectedOrders(
    List<OrderBookModel> allOrders,
  ) async {
    final selectedCount = _multiSelectCubit.selectedCount;
    if (selectedCount == 0) return;

    final confirmed = await DerivativesOrderCancelConfirmationDialog.show(
      context,
      orderCount: selectedCount,
    );

    if (confirmed == true) {
      await _multiSelectCubit.cancelSelectedOrders(allOrders);

      // Listen for success/error and refresh data
      final state = _multiSelectCubit.state;
      if (state.isSuccess) {
        await _cubit.loadData();
        if (mounted) {
          showSnackBar(
            context,
            state.successMessage ?? 'Đã hủy lệnh thành công',
            isSuccess: true,
          );
        }
      } else if (state.errorMessage != null) {
        if (mounted) {
          showSnackBar(context, state.errorMessage!, isSuccess: false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DerivativesOrderBookCubit, DerivativesOrderBookState>(
      builder: (context, orderState) {
        return BlocBuilder<
          DerivativesOrderBookMultiSelectCubit,
          DerivativesOrderBookMultiSelectState
        >(
          bloc: _multiSelectCubit,
          builder: (context, multiSelectState) {
            return Scaffold(
              body: Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Column(
                  children: [
                    if (orderState.isLoading)
                      const Expanded(child: CommandHistoryLoadingWidget()),
                    if (!orderState.isLoading && orderState.listItems.isEmpty)
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await _cubit.loadData();
                          },
                          child: NoDataView(
                            content:
                                VPTradingLocalize
                                    .current
                                    .trading_no_data_message,
                          ),
                        ),
                      ),
                    if (orderState.listItems.isNotEmpty &&
                        !orderState.isLoading) ...[
                      DerivativesOrderTitleWidget(
                        expandTitleWidget: const [10, 6, 7, 12],
                        showTitleDeleteAll: true,
                        onDeleteAll: () {
                          // TODO: Implement cancel all orders
                        },
                        onMultiSelectMode: _handleEnterMultiSelectMode,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: PullToRefreshView(
                          onRefresh: () async {
                            await _cubit.loadData();
                          },
                          child: DerivativesOrderList(
                            items: orderState.listItems,
                            refresh: () async {
                              await _cubit.loadData();
                            },
                            editSuccess: () async {
                              await _cubit.loadData();
                            },
                            isMultiSelectMode:
                                multiSelectState.isMultiSelectMode,
                            isOrderSelected: _multiSelectCubit.isOrderSelected,
                            onSelectionChanged:
                                _multiSelectCubit.toggleOrderSelection,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              bottomNavigationBar:
                  multiSelectState.isMultiSelectMode
                      ? DerivativesOrderMultiSelectBottomBar(
                        selectedCount: multiSelectState.selectedCount,
                        onBack: _handleExitMultiSelectMode,
                        onCancelOrders:
                            () => _handleCancelSelectedOrders(
                              orderState.listItems,
                            ),
                        isLoading: multiSelectState.isCancelling,
                      )
                      : null,
            );
          },
        );
      },
    );
  }
}
