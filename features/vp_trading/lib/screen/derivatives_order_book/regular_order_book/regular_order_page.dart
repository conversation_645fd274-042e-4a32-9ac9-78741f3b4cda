import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/derivatives_order_list.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/derivatives_order_title_widget.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class RegularOrderPage extends StatefulWidget {
  const RegularOrderPage({super.key});

  @override
  State<RegularOrderPage> createState() => _RegularOrderPageState();
}

class _RegularOrderPageState extends State<RegularOrderPage> {
  late DerivativesOrderBookCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<DerivativesOrderBookCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DerivativesOrderBookCubit, DerivativesOrderBookState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Column(
            children: [
              if (state.isLoading)
                const Expanded(child: CommandHistoryLoadingWidget()),
              if (!state.isLoading && state.listItems.isEmpty)
                Expanded(
                  child: PullToRefreshView(
                    onRefresh: () async {
                      await _cubit.loadData();
                    },
                    child: NoDataView(
                      content:
                          VPTradingLocalize.current.trading_no_data_message,
                    ),
                  ),
                ),
              if (state.listItems.isNotEmpty && !state.isLoading) ...[
                DerivativesOrderTitleWidget(
                  expandTitleWidget: const [10, 6, 7, 12],
                  showTitleDeleteAll: true,
                  onDeleteAll: () {
                    // TODO: Implement cancel all orders
                  },
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: PullToRefreshView(
                    onRefresh: () async {
                      await _cubit.loadData();
                    },
                    child: DerivativesOrderList(
                      items: state.listItems,

                      refresh: () async {
                        await _cubit.loadData();
                      },
                      editSuccess: () async {
                        await _cubit.loadData();
                      },
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
