import 'package:flutter/material.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/derivatives_order_item.dart';

class DerivativesOrderList extends StatefulWidget {
  final List<OrderBookModel> items;
  final Future<void> Function() refresh;
  final Future<void> Function() editSuccess;
  final bool? shrinkWrap;
  final bool isMultiSelectMode;
  final bool Function(String)? isOrderSelected;
  final ValueChanged<String>? onSelectionChanged;

  const DerivativesOrderList({
    super.key,
    required this.items,
    required this.refresh,
    this.shrinkWrap = false,
    required this.editSuccess,
    this.isMultiSelectMode = false,
    this.isOrderSelected,
    this.onSelectionChanged,
  });

  @override
  State<DerivativesOrderList> createState() => _DerivativesOrderListState();
}

class _DerivativesOrderListState extends State<DerivativesOrderList> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      shrinkWrap: widget.shrinkWrap ?? false,
      itemCount: widget.items.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final item = widget.items[index];
        return DerivativesOrderItem(
          item: item,
          onTap: () {},
          onEdit: () async {
            // TODO: Implement edit functionality
            await widget.editSuccess();
          },
          onCancel: () async {
            // TODO: Implement cancel functionality
            await widget.refresh();
          },
          isMultiSelectMode: widget.isMultiSelectMode,
          isSelected: widget.isOrderSelected?.call(item.orderId ?? '') ?? false,
          onSelectionChanged: widget.onSelectionChanged,
        );
      },
    );
  }
}
