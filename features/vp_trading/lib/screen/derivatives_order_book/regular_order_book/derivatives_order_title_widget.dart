import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';

class DerivativesOrderTitleWidget extends StatelessWidget {
  final List<int> expandTitleWidget;
  final bool showTitleDeleteAll;
  final VoidCallback? onDeleteAll;

  const DerivativesOrderTitleWidget({
    super.key,
    required this.expandTitleWidget,
    this.showTitleDeleteAll = false,
    this.onDeleteAll,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showTitleDeleteAll) ...[
          Row(
            children: [
              Expanded(
                child: VpsButton.secondaryDangerXsSmall(
                  title: VPTradingLocalize.current.trading_cancel_all_order,
                  onPressed: onDeleteAll,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: VpsButton.secondaryDangerXsSmall(
                  title: '<PERSON><PERSON><PERSON> nhi<PERSON> lệnh',
                  onPressed: onDeleteAll,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: vpColor.backgroundElevation1,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  'Mã HĐ/ Trạng thái',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              SizedBox(
                width: 94,
                child: Text(
                  'KL đặt/ Giá đặt',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const SizedBox(width: 16),
              SizedBox(
                width: 96,
                child: Text(
                  'Thao tác',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
