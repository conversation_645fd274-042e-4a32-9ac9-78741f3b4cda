import 'package:flutter/material.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/new_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/widgets/content_expansion_widget.dart';
import 'package:vp_trading/widgets/status_order_widget.dart';

class DerivativesOrderItem extends StatefulWidget {
  final OrderBookModel item;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onCancel;
  final bool isMultiSelectMode;
  final bool isSelected;
  final ValueChanged<String>? onSelectionChanged;

  const DerivativesOrderItem({
    super.key,
    required this.item,
    required this.onTap,
    required this.onEdit,
    required this.onCancel,
    this.isMultiSelectMode = false,
    this.isSelected = false,
    this.onSelectionChanged,
  });

  @override
  State<DerivativesOrderItem> createState() => _DerivativesOrderItemState();
}

class _DerivativesOrderItemState extends State<DerivativesOrderItem>
    with SingleTickerProviderStateMixin {
  bool isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      isExpanded = !isExpanded;
    });

    if (isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(16),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _toggleExpansion,
        child: Column(
          children: [
            _buildCollapsedContent(context),
            SizeTransition(
              sizeFactor: _expandAnimation,
              child: _buildExpandedContent(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollapsedContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (widget.isMultiSelectMode) ...[
            _buildCheckbox(context),
            const SizedBox(width: 12),
          ],
          _buildSideIndicator(context),
          const SizedBox(width: 16),
          _buildOrderInfo(context),
          const SizedBox(width: 16),
          _buildQuantityPrice(context),
          const SizedBox(width: 16),
          widget.isMultiSelectMode
              ? const SizedBox.shrink()
              : _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.item.symbol ?? '',
            style: context.textStyle.subtitle16?.copyWith(
              color: vpColor.textPrimary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          _buildStatusTag(context),
        ],
      ),
    );
  }

  Widget _buildQuantityPrice(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '${widget.item.execQty ?? 0}/${widget.item.qty ?? 0}',
          style: context.textStyle.body14?.copyWith(color: vpColor.textPrimary),
        ),
        const SizedBox(height: 2),
        Text(
          FormatUtils.formatNumberMaxTwoDecimals(
                double.tryParse(widget.item.price ?? '0'),
              ) ??
              '-',
          style: context.textStyle.captionRegular?.copyWith(
            color: vpColor.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildActionButton(
          onTap: widget.onEdit,
          icon: VpTradingAssets.icons.icEdit.svg(),
        ),
        const SizedBox(width: 8),
        SizedBox(
          height: 24,
          child: VerticalDivider(width: 1, color: vpColor.textDisabled),
        ),
        const SizedBox(width: 8),
        _buildActionButton(
          onTap: widget.onCancel,
          icon: VpTradingAssets.icons.icRemove2.svg(),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required VoidCallback onTap,
    required Widget icon,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation1,
          shape: BoxShape.circle,
          border: Border.all(color: vpColor.textDisabled, width: 0.5),
        ),
        padding: const EdgeInsets.all(8),
        child: icon,
      ),
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          ContentExpansionWidget(
            title: 'Thời gian đặt',
            value: widget.item.tradeTime ?? '08:30:04',
          ),
          ContentExpansionWidget(
            title: 'Số hiệu lệnh',
            value: widget.item.orderId ?? '21193',
          ),
          ContentExpansionWidget(
            title: 'Khối lượng khớp',
            value: '${widget.item.execQty ?? 0}',
          ),
          ContentExpansionWidget(
            title: 'Giá khớp TB',
            value:
                FormatUtils.formatNumberMaxTwoDecimals(widget.item.execPrice) ??
                '1194.2',
          ),
          ContentExpansionWidget(title: 'Kênh đặt', value: widget.item.viaName),
          const ContentExpansionWidget(
            title: 'Ghi chú',
            value: 'Lệnh bán xử lý tự động',
          ),
        ],
      ),
    );
  }

  Widget _buildSideIndicator(BuildContext context) {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: _getSideColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Center(
        child: Text(
          _getSideText(),
          style: context.textStyle.subtitle14?.copyWith(color: _getSideColor()),
        ),
      ),
    );
  }

  Widget _buildStatusTag(BuildContext context) {
    return StatusOrderNewWidget(
      textStatus: widget.item.newOrderStatusEnum.label,
      colorStatus: widget.item.newOrderStatusEnum.color,
      colorText: widget.item.newOrderStatusEnum.textColor,
    );
  }

  Widget _buildCheckbox(BuildContext context) {
    final canCancel = widget.item.allowCancel == 'Y';

    return GestureDetector(
      onTap:
          canCancel
              ? () {
                widget.onSelectionChanged?.call(widget.item.orderId ?? '');
              }
              : null,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color:
              widget.isSelected
                  ? vpColor.backgroundBrand
                  : vpColor.backgroundElevation0,
          border: Border.all(
            color:
                canCancel
                    ? (widget.isSelected
                        ? vpColor.backgroundBrand
                        : vpColor.strokePrimary)
                    : vpColor.textDisabled,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child:
            widget.isSelected
                ? Icon(
                  Icons.check,
                  size: 16,
                  color: vpColor.backgroundElevation0,
                )
                : null,
      ),
    );
  }

  String _getSideText() {
    return widget.item.orderTypeEnum == OrderTypeEnum.buy ? 'L' : 'S';
  }

  Color _getSideColor() {
    return widget.item.orderTypeEnum == OrderTypeEnum.buy
        ? vpColor.textAccentGreen
        : vpColor.textAccentRed;
  }
}
