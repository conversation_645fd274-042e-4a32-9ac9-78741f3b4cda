import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

/// Confirmation dialog for cancelling multiple orders
class DerivativesOrderCancelConfirmationDialog extends StatelessWidget {
  /// Number of orders to be cancelled
  final int orderCount;
  
  /// Callback when user confirms cancellation
  final VoidCallback onConfirm;
  
  /// Callback when user cancels the action
  final VoidCallback onCancel;

  const DerivativesOrderCancelConfirmationDialog({
    super.key,
    required this.orderCount,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return VPDialog(
      title: '<PERSON><PERSON><PERSON> nhận hủy lệnh',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: 48,
            color: vpColor.iconAccentRed,
          ),
          const SizedBox(height: 16),
          Text(
            '<PERSON><PERSON><PERSON> có chắc chắn muốn hủy $orderCount lệnh đã chọn?',
            style: context.textStyle.bodyMedium?.copyWith(
              color: vpColor.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Thao tác này không thể hoàn tác.',
            style: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        VpsButton.secondary(
          onPressed: onCancel,
          child: Text(
            'Hủy bỏ',
            style: context.textStyle.buttonMedium?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
        ),
        VPButton.primary(
          onPressed: onConfirm,
          backgroundColor: vpColor.backgroundDanger,
          child: Text(
            'Xác nhận hủy',
            style: context.textStyle.buttonMedium?.copyWith(
              color: vpColor.textWhite,
            ),
          ),
        ),
      ],
    );
  }

  /// Show the confirmation dialog
  static Future<bool?> show(
    BuildContext context, {
    required int orderCount,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => DerivativesOrderCancelConfirmationDialog(
        orderCount: orderCount,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }
}
