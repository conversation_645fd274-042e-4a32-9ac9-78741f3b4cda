import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

/// Bottom action bar for multi-select mode in derivatives order book
class DerivativesOrderMultiSelectBottomBar extends StatelessWidget {
  /// Number of selected orders
  final int selectedCount;
  
  /// Callback when back button is pressed
  final VoidCallback onBack;
  
  /// Callback when cancel orders button is pressed
  final VoidCallback onCancelOrders;
  
  /// Whether cancellation is in progress
  final bool isLoading;

  const DerivativesOrderMultiSelectBottomBar({
    super.key,
    required this.selectedCount,
    required this.onBack,
    required this.onCancelOrders,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        boxShadow: [
          BoxShadow(
            color: vpColor.textPrimary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON><PERSON>(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Back button
              Expanded(
                child: VPButton.secondary(
                  onPressed: isLoading ? null : onBack,
                  child: Text(
                    'Quay lại',
                    style: context.textStyle.buttonMedium?.copyWith(
                      color: vpColor.primary,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Cancel orders button
              Expanded(
                child: VPButton.primary(
                  onPressed: selectedCount > 0 && !isLoading 
                      ? onCancelOrders 
                      : null,
                  loading: isLoading,
                  child: Text(
                    selectedCount > 0 
                        ? 'Hủy $selectedCount lệnh'
                        : 'Chọn lệnh để hủy',
                    style: context.textStyle.buttonMedium?.copyWith(
                      color: selectedCount > 0 && !isLoading
                          ? vpColor.backgroundElevation0
                          : vpColor.textDisabled,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
