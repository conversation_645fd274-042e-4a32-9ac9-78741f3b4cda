import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';

class ConditionalOrderPage extends StatelessWidget {
  const ConditionalOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: vpColor.backgroundElevation0,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.construction, size: 64, color: vpColor.iconSecondary),
              const SizedBox(height: 16),
              Text(
                'Lệnh có điều kiện',
                style: context.textStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Tính năng này sẽ được triển khai trong phiên bản tiếp theo.',
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
