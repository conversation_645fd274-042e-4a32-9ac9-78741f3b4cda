import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/derivatives_order_book/widget/conditional_order_page.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/regular_order_page.dart';

class DerivativesOrderBookScreen extends StatefulWidget {
  const DerivativesOrderBookScreen({super.key});

  @override
  State<DerivativesOrderBookScreen> createState() =>
      _DerivativesOrderBookScreenState();
}

class _DerivativesOrderBookScreenState extends State<DerivativesOrderBookScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late DerivativesOrderBookCubit _cubit;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _cubit = DerivativesOrderBookCubit()..init();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _cubit,
      child: VPScaffold(
        appBar: VPAppBar.layer(
          backgroundColor: vpColor.backgroundElevation0,
          title: "Sổ lệnh",
        ),
        body: _buildTabBarView(),
      ),
    );
  }

  Widget _buildTabBarView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ColoredBox(
          color: vpColor.backgroundElevation0,
          child: VPTabBar(
            controller: _tabController,
            tabs: [
              Tab(text: VPTradingLocalize.current.trading_normal),
              Tab(text: VPTradingLocalize.current.trading_order_type_condition),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: const RangeMaintainingScrollPhysics(),
            children: const [RegularOrderPage(), ConditionalOrderPage()],
          ),
        ),
      ],
    );
  }
}
