part of 'derivatives_order_book_cubit.dart';

final class DerivativesOrderBookState extends Equatable {
  final bool isLoading;
  final List<OrderBookModel> listItems;
  final OrderBookRequest request;
  final String? errorMessage;
  final FilterNormalParam? filterParam;
  final CommonPagingState pagingState;

  const DerivativesOrderBookState({
    this.isLoading = false,
    this.errorMessage,
    this.filterParam,
    this.listItems = const [],
    this.request = const OrderBookRequest(pageNo: 1, pageSize: 100),
    this.pagingState = const CommonPagingState(),
  });

 
  int get nextPage => pagingState.nextPage;
  int get pageSize => pagingState.pageSize;

  @override
  List<Object?> get props => [
    isLoading,
    errorMessage,
    listItems,
    request,
    filterParam,
    pagingState,
  ];

  DerivativesOrderBookState copyWith({
    bool? isLoading,
    String? errorMessage,
    List<OrderBookModel>? listItems,
    OrderBookRequest? request,
    FilterNormalParam? filterParam,
    CommonPagingState? pagingState,
  }) {
    return DerivativesOrderBookState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      listItems: listItems ?? this.listItems,
      request: request ?? this.request,
      filterParam: filterParam ?? this.filterParam,
      pagingState: pagingState ?? this.pagingState,
    );
  }
}
