import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/common_paging_state/common_paging_state.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';

part 'derivatives_order_book_state.dart';

class DerivativesOrderBookCubit extends Cubit<DerivativesOrderBookState> {
  DerivativesOrderBookCubit() : super(const DerivativesOrderBookState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void init() {
    emit(
      state.copyWith(
        filterParam: FilterNormalParam(
          subAccountModel:
              GetIt.instance<SubAccountCubit>()
                  .state
                  .derivativeSubAccount
                  .firstOrNull,
          orderStatus: [OrderStatusEnum.all],
        ),
        request: state.request.copyWith(
          accountId:
              GetIt.instance<SubAccountCubit>()
                  .state
                  .derivativeSubAccount
                  .firstOrNull
                  ?.id ??
              "",
          productTypeCd: "PS",
        ),
      ),
    );
    loadData();
  }

  Future<void> loadData({OrderBookRequest? queryCustom}) async {
    try {
      emit(state.copyWith(isLoading: true));

      final result = await _commandHistoryRepository.getOrder(
        queries:
            (queryCustom != null)
                ? queryCustom
                : state.request.copyWith(pageNo: 1),
      );

      final items = result.data?.content ?? [];
      final hasMore = items.isNotEmpty && items.length >= state.pageSize;

      emit(
        state.copyWith(
          isLoading: false,
          listItems: items,
          pagingState: state.pagingState.copyWith(
            currentPage: 1,
            hasMore: hasMore,
          ),
        ),
      );
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  Future<void> updateFilterStatus(List<OrderStatusEnum> orderStatus) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(orderStatus: orderStatus),
        request: state.request.copyWith(
          orderStatus: orderStatus.map((e) => e.codeRequest).join(','),
        ),
      ),
    );
    await loadData();
  }

  Future<void> updateFilterAccount(SubAccountModel subAccountModel) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(
          subAccountModel: subAccountModel,
        ),
        request: state.request.copyWith(
          accountId: subAccountModel.id ?? "",
          productTypeCd: subAccountModel.productTypeCd ?? "",
        ),
      ),
    );
    await loadData();
  }

  void updateCurrentFilter(FilterNormalParam filterParam) {
    emit(
      state.copyWith(
        filterParam: filterParam,
        request: state.request.copyWith(
          symbol: filterParam.symbol,
          orderStatus: filterParam.orderStatus
              ?.map((e) => e.codeRequest)
              .join(','),
        ),
      ),
    );
    loadData();
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}
