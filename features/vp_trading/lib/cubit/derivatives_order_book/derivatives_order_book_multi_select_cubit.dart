import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';

part 'derivatives_order_book_multi_select_state.dart';

/// Cubit for managing multi-select functionality in derivatives order book
class DerivativesOrderBookMultiSelectCubit 
    extends Cubit<DerivativesOrderBookMultiSelectState> {
  DerivativesOrderBookMultiSelectCubit() 
      : super(const DerivativesOrderBookMultiSelectState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  /// Enter multi-select mode
  void enterMultiSelectMode() {
    emit(state.copyWith(
      isMultiSelectMode: true,
      selectedOrderIds: <String>{},
    ));
  }

  /// Exit multi-select mode and clear all selections
  void exitMultiSelectMode() {
    emit(state.copyWith(
      isMultiSelectMode: false,
      selectedOrderIds: <String>{},
      isCancelling: false,
      errorMessage: null,
    ));
  }

  /// Toggle selection for a specific order
  void toggleOrderSelection(String orderId) {
    if (!state.isMultiSelectMode) return;

    final selectedOrderIds = Set<String>.from(state.selectedOrderIds);
    
    if (selectedOrderIds.contains(orderId)) {
      selectedOrderIds.remove(orderId);
    } else {
      selectedOrderIds.add(orderId);
    }

    emit(state.copyWith(selectedOrderIds: selectedOrderIds));
  }

  /// Check if an order is selected
  bool isOrderSelected(String orderId) {
    return state.selectedOrderIds.contains(orderId);
  }

  /// Get the count of selected orders
  int get selectedCount => state.selectedOrderIds.length;

  /// Get list of selected order IDs
  List<String> get selectedOrderIds => state.selectedOrderIds.toList();

  /// Cancel all selected orders
  Future<void> cancelSelectedOrders(List<OrderBookModel> allOrders) async {
    if (state.selectedOrderIds.isEmpty) return;

    try {
      emit(state.copyWith(isCancelling: true, errorMessage: null));

      // Filter orders that are selected and can be cancelled
      final ordersToCancel = allOrders
          .where((order) => 
              state.selectedOrderIds.contains(order.orderId) &&
              order.allowCancel == 'Y')
          .toList();

      if (ordersToCancel.isEmpty) {
        emit(state.copyWith(
          isCancelling: false,
          errorMessage: 'Không có lệnh nào có thể hủy được',
        ));
        return;
      }

      // Cancel orders sequentially
      final List<String> failedOrders = [];
      int successCount = 0;

      for (final order in ordersToCancel) {
        try {
          final request = DeleteOrderRequest(
            orderId: order.orderId ?? '',
            accountId: order.accountId ?? '',
            market: "derivative",
            requestId: "Prefix: ${AppHelper().genXRequestID()}",
            via: "V",
          );

          final response = await _commandHistoryRepository.deleteOrder(request);
          
          if (response.isSuccess) {
            successCount++;
          } else {
            failedOrders.add(order.symbol ?? order.orderId ?? '');
          }
        } catch (e) {
          failedOrders.add(order.symbol ?? order.orderId ?? '');
          debugPrint('Error cancelling order ${order.orderId}: $e');
        }
      }

      // Emit result
      if (failedOrders.isEmpty) {
        emit(state.copyWith(
          isCancelling: false,
          isSuccess: true,
          successMessage: 'Đã hủy thành công $successCount lệnh',
        ));
        // Auto exit multi-select mode after successful cancellation
        Future.delayed(const Duration(milliseconds: 500), () {
          exitMultiSelectMode();
        });
      } else {
        emit(state.copyWith(
          isCancelling: false,
          errorMessage: failedOrders.length == ordersToCancel.length
              ? 'Không thể hủy các lệnh đã chọn'
              : 'Đã hủy $successCount lệnh. Không thể hủy: ${failedOrders.join(", ")}',
        ));
      }
    } catch (error) {
      emit(state.copyWith(
        isCancelling: false,
        errorMessage: await getErrorMessage(error),
      ));
    }
  }

  /// Clear error message
  void clearErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Clear success message
  void clearSuccessMessage() {
    emit(state.copyWith(isSuccess: false, successMessage: null));
  }

  /// Reset state
  void reset() {
    emit(const DerivativesOrderBookMultiSelectState());
  }
}
