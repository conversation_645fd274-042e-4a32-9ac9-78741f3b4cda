import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_stock_common/model/place_order_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/derivatives_order_book/derivatives_order_book_screen.dart';
import 'package:vp_trading/screen/derivatives_portfolio/derivatives_portfolio_screen.dart';
import 'package:vp_trading/screen/place_order/main/trading_order_main_screen.dart';
import 'package:vp_trading/screen/place_order/place_order_page.dart';

enum TradingRouter {
  placeOrder('/placeOrder'),
  derivativesPortfolio('/derivatives_portfolio'),
  derivativesOrderBookScreen('/derivativesOrderBookScreen');

  final String routeName;

  const TradingRouter(this.routeName);
}

List<RouteBase> tradingRouter() {
  return [
    GoRoute(
      name: TradingRouter.placeOrder.routeName,
      path: TradingRouter.placeOrder.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: TradingOrderMainScreen(
            argument: PlaceOrderArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          ),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativesPortfolio.routeName,
      path: TradingRouter.derivativesPortfolio.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativesPortfolioScreen(),
        );
      },
    ),
    GoRoute(
      name: TradingRouter.derivativesOrderBookScreen.routeName,
      path: TradingRouter.derivativesOrderBookScreen.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: const DerivativesOrderBookScreen(),
        );
      },
    ),
  ];
}
